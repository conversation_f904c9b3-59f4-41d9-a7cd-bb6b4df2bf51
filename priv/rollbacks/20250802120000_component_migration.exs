defmodule RivaAsh.Repo.Migrations.RollbackComponentMigration do
  use Ecto.Migration

  def change do
    # Reverse core components move
    File.rename!(
      "lib/riva_ash_web/components/organisms/core_components/core_components.ex",
      "lib/riva_ash_web/components/core_components.ex"
    )

    # Reverse interactive component reclassification
    interactive_components = [
      {"molecules/availability_grid.ex", "interactive/availability_grid.ex"},
      {"molecules/daily_schedule.ex", "interactive/daily_schedule.ex"},
      {"organisms/layout/grid_position_picker.ex", "interactive/grid_position_picker.ex"},
      {"molecules/monthly_calendar.ex", "interactive/monthly_calendar.ex"},
      {"organisms/layout/designer.ex", "interactive/plot_layout_designer.ex"},
      {"organisms/reservations/recurrence_pattern.ex", "interactive/recurrence_pattern.ex"},
      {"molecules/time_slot_picker.ex", "interactive/time_slot_picker.ex"},
      {"molecules/weekly_calendar.ex", "interactive/weekly_calendar.ex"}
    ]

    Enum.each(interactive_components, fn {src, dest} ->
      File.rename!(
        "lib/riva_ash_web/components/#{src}",
        "lib/riva_ash_web/components/#{dest}"
      )
    end)

    # <PERSON><PERSON><PERSON> created directories
    File.rm_rf!("lib/riva_ash_web/components/organisms/core_components")
    File.rm_rf!("lib/riva_ash_web/components/templates/layouts")
  end
end

/** @type {import('tailwindcss').Config} */
module.exports = {
  important: ".riva-ash-web",
  darkMode: 'class',
  content: [
    "../deps/salad_ui/lib/**/*.ex",
    "../lib/**/*.{ex,heex,eex}",
    "../lib/**/*.{js,jsx,ts,tsx}",
    "./js/**/*.{js,jsx,ts,tsx}",
    "../priv/static/**/*.{js,css}",
    "../storybook/**/*.exs",
    "../storybook/**/*.exs"
  ],
  theme: {
    extend: {
      fontSize: {
        xs: ["0.75rem", { lineHeight: "1rem" }],
        sm: ["0.875rem", { lineHeight: "1.25rem" }],
        base: ["1rem", { lineHeight: "1.5rem" }],
        lg: ["1.125rem", { lineHeight: "1.75rem" }],
        xl: ["1.25rem", { lineHeight: "1.75rem" }],
        "2xl": ["1.5rem", { lineHeight: "2rem" }],
        "3xl": ["1.875rem", { lineHeight: "2.25rem" }],
        "4xl": ["2.25rem", { lineHeight: "2.5rem" }],
        "5xl": ["3rem", { lineHeight: "1" }],
        "6xl": ["3.75rem", { lineHeight: "1" }],
        "7xl": ["4.5rem", { lineHeight: "1" }],
        "8xl": ["6rem", { lineHeight: "1" }],
        "9xl": ["8rem", { lineHeight: "1" }],
      },
      spacing: {
        px: "1px",
        0: "0",
        0.5: "0.125rem",
        1: "0.25rem",
        1.5: "0.375rem",
        2: "0.5rem",
        2.5: "0.625rem",
        3: "0.75rem",
        3.5: "0.875rem",
        4: "1rem",
        5: "1.25rem",
        6: "1.5rem",
        7: "1.75rem",
        8: "2rem",
        9: "2.25rem",
        10: "2.5rem",
        11: "2.75rem",
        12: "3rem",
        14: "3.5rem",
        16: "4rem",
        20: "5rem",
        24: "6rem",
        28: "7rem",
        32: "8rem",
        36: "9rem",
        40: "10rem",
        44: "11rem",
        48: "12rem",
        52: "13rem",
        56: "14rem",
        60: "15rem",
        64: "16rem",
        72: "18rem",
        80: "20rem",
        96: "24rem",
      },
      borderRadius: {
        none: "0px",
        sm: "0.125rem",
        DEFAULT: "0.25rem",
        md: "0.375rem",
        lg: "0.5rem",
        xl: "0.75rem",
        "2xl": "1rem",
        "3xl": "1.5rem",
        full: "9999px",
      },
      colors: {
        primary: "hsl(var(--riva-primary) / <alpha-value>)",
        secondary: "hsl(var(--riva-secondary) / <alpha-value>)",
        accent: "hsl(var(--riva-accent) / <alpha-value>)",
        destructive: "hsl(var(--riva-destructive))",
        background: "hsl(var(--riva-background))",
        foreground: "hsl(var(--riva-foreground))",
        border: "hsl(var(--riva-border))",
        input: "hsl(var(--riva-input))",
        ring: "hsl(var(--riva-ring))",
        chart: {
          1: "hsl(var(--riva-chart-1))",
          2: "hsl(var(--riva-chart-2))",
          3: "hsl(var(--riva-chart-3))",
          4: "hsl(var(--riva-chart-4))",
          5: "hsl(var(--riva-chart-5))",
        },
        sidebar: {
          background: "hsl(var(--riva-sidebar-background))",
          foreground: "hsl(var(--riva-sidebar-foreground))",
          primary: "hsl(var(--riva-sidebar-primary))",
          "primary-foreground": "hsl(var(--riva-sidebar-primary-foreground))",
          accent: "hsl(var(--riva-sidebar-accent))",
          "accent-foreground": "hsl(var(--riva-sidebar-accent-foreground))",
          border: "hsl(var(--riva-sidebar-border))",
          ring: "hsl(var(--riva-sidebar-ring))",
        }
      },
    },
  },
  plugins: [],
}

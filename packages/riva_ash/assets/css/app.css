@import "./salad_ui.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: oklch(0.9906 0.0017 247.8388);
    --foreground: oklch(0.1687 0.0065 271.0139);
    --card: oklch(0.9797 0.0026 286.3509);
    --card-foreground: oklch(0.1993 0.0068 258.3682);
    --popover: oklch(0.9700 0.0029 264.5420);
    --popover-foreground: oklch(0.1809 0.0052 248.1162);
    --primary: oklch(0.5207 0.1799 274.9571);
    --primary-foreground: oklch(0.9794 0.0013 286.3756);
    --secondary: oklch(0.6505 0.1002 184.7808);
    --secondary-foreground: oklch(0.1492 0.0093 263.9667);
    --muted: oklch(0.9187 0.0029 264.5413);
    --muted-foreground: oklch(0.4495 0.0085 268.4594);
    --accent: oklch(0.7050 0.1673 58.7984);
    --accent-foreground: oklch(0.1492 0.0093 263.9667);
    --destructive: oklch(0.5494 0.1993 25.1156);
    --destructive-foreground: oklch(0.9794 0.0013 286.3756);
    --border: oklch(0.8507 0.0047 258.3267);
    --input: oklch(0.8999 0.0046 258.3258);
    --ring: oklch(0.5207 0.1799 274.9571);
    --chart-1: oklch(0.5207 0.1799 274.9571);
    --chart-2: oklch(0.6505 0.1002 184.7808);
    --chart-3: oklch(0.7050 0.1673 58.7984);
    --chart-4: oklch(0.5497 0.1211 329.8217);
    --chart-5: oklch(0.5078 0.1510 146.9856);
    --sidebar: oklch(0.9607 0.0017 247.8395);
    --sidebar-foreground: oklch(0.2511 0.0064 258.3609);
    --sidebar-primary: oklch(0.5207 0.1799 274.9571);
    --sidebar-primary-foreground: oklch(0.9794 0.0013 286.3756);
    --sidebar-accent: oklch(0.7050 0.1673 58.7984);
    --sidebar-accent-foreground: oklch(0.1492 0.0093 263.9667);
    --sidebar-border: oklch(0.8793 0.0042 271.3643);
    --sidebar-ring: oklch(0.5207 0.1799 274.9571);
    --font-sans: "Inter", system-ui, sans-serif;
    --font-serif: Lora, ui-serif, serif;
    --font-mono: "IBM Plex Mono", monospace;
    --radius: 6px;
    --shadow-2xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
    --shadow-xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
    --shadow-sm: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
    --shadow: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
    --shadow-md: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 2px 4px -1px hsl(0 0% 0% / 1.00);
    --shadow-lg: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 4px 6px -1px hsl(0 0% 0% / 1.00);
    --shadow-xl: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 8px 10px -1px hsl(0 0% 0% / 1.00);
    --shadow-2xl: 4px 4px 0px 0px hsl(0 0% 0% / 2.50);
    --tracking-normal: -0.025em;
    --spacing: 0.3rem;
  }

  .dark {
    --background: oklch(0.1395 0.0048 262.8021);
    --foreground: oklch(0.9194 0.0027 286.3469);
    --card: oklch(0.1588 0.0045 264.4376);
    --card-foreground: oklch(0.9001 0.0017 247.8412);
    --popover: oklch(0.1809 0.0052 248.1162);
    --popover-foreground: oklch(0.9001 0.0017 247.8412);
    --primary: oklch(0.5999 0.1601 274.8551);
    --primary-foreground: oklch(0 0 0);
    --secondary: oklch(0.7007 0.0902 185.1086);
    --secondary-foreground: oklch(0 0 0);
    --muted: oklch(0.1993 0.0068 258.3682);
    --muted-foreground: oklch(0.7005 0.0074 247.9322);
    --accent: oklch(0.7544 0.1677 66.2271);
    --accent-foreground: oklch(0 0 0);
    --destructive: oklch(0.6494 0.1796 25.0282);
    --destructive-foreground: oklch(0 0 0);
    --border: oklch(0.2505 0.0088 255.5892);
    --input: oklch(0.2213 0.0066 258.3651);
    --ring: oklch(0.5999 0.1601 274.8551);
    --chart-1: oklch(0.5999 0.1601 274.8551);
    --chart-2: oklch(0.7007 0.0902 185.1086);
    --chart-3: oklch(0.7544 0.1677 66.2271);
    --chart-4: oklch(0.6499 0.1397 330.2309);
    --chart-5: oklch(0.6488 0.1400 150.0147);
    --sidebar: oklch(0.1588 0.0045 264.4376);
    --sidebar-foreground: oklch(1.0000 0 0);
    --sidebar-primary: oklch(0.7044 0.1872 23.1858);
    --sidebar-primary-foreground: oklch(0 0 0);
    --sidebar-accent: oklch(0.6755 0.1765 252.2592);
    --sidebar-accent-foreground: oklch(0 0 0);
    --sidebar-border: oklch(0.2805 0.0079 264.4401);
    --sidebar-ring: oklch(0.7044 0.1872 23.1858);
    --font-sans: DM Sans, sans-serif;
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: Space Mono, monospace;
    --radius: 0px;
    --shadow-2xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
    --shadow-xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
    --shadow-sm: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
    --shadow: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
    --shadow-md: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 2px 4px -1px hsl(0 0% 0% / 1.00);
    --shadow-lg: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 4px 6px -1px hsl(0 0% 0% / 1.00);
    --shadow-xl: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 8px 10px -1px hsl(0 0% 0% / 1.00);
    --shadow-2xl: 4px 4px 0px 0px hsl(0 0% 0% / 2.50);
  }

  @theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);

    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-normal: var(--tracking-normal);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }

  body {
    letter-spacing: var(--tracking-normal);
  }

  * {
    @apply border-border !important;
  }
}



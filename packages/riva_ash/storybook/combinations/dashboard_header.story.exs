defmodule RivaAshWeb.Storybook.Combinations.DashboardHeader do
  use PhoenixStorybook.Story, :component
  alias RivaAshWeb.Components.AtomicComponents

  def function, do: &AtomicComponents.organisms/0

  def variations do
    [
      %Variation{
        id: :default,
        attributes: %{
          user: %{
            name: "<PERSON>",
            role: "Manager",
            notification_count: 3
          }
        },
        slots: [
          """
          <div class="flex items-center justify-between p-4 bg-white shadow-sm">
            <AtomicComponents.molecules.search_bar placeholder="Search reservations..." />

            <div class="flex items-center space-x-4">
              <AtomicComponents.molecules.notification_toast count={@user.notification_count}>
                <:icon name="bell" />
              </AtomicComponents.molecules.notification_toast>

              <AtomicComponents.atoms.avatar
                name={@user.name}
                role={@user.role}
                size="md"
                variant="circle"
              />
            </div>
          </div>
          """
        ]
      },
      %Variation{
        id: :with_active_notifications,
        attributes: %{
          user: %{
            name: "<PERSON>",
            role: "<PERSON><PERSON>",
            notification_count: 12
          }
        },
        slots: [
          """
          <div class="flex items-center justify-between p-4 bg-gray-50 border-b">
            <AtomicComponents.molecules.search_bar
              placeholder="Find customers..."
              variant="dark"
            />

            <div class="flex items-center gap-6">
              <AtomicComponents.molecules.notification_toast
                count={@user.notification_count}
                variant="urgent"
              >
                <:icon name="alert-circle" />
              </AtomicComponents.molecules.notification_toast>

              <AtomicComponents.atoms.avatar
                name={@user.name}
                src="/images/admin-avatar.png"
                size="lg"
                status="online"
              />
            </div>
          </div>
          """
        ]
      }
    ]
  end
end

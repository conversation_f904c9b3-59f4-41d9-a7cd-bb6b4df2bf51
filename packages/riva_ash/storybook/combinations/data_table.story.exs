defmodule RivaAshWeb.Storybook.Combinations.DataTable do
  use PhoenixStorybook.Story, :component
  alias RivaAshWeb.Components.AtomicComponents

  def function, do: &AtomicComponents.organisms/1

  def variations do
    [
      %Variation{
        id: :paginated,
        attributes: %{
          data: Enum.map(1..50, &%{id: &1, name: "Item #{&1}", status: "active"}),
          page_size: 10,
          current_page: 1
        },
        slots: [
          """
          <:action>
            <AtomicComponents.molecules.pagination_controls
              current_page={@current_page}
              total_pages={5}
              phx-change="page_changed"
            />
          </:action>
          """
        ]
      }
    ]
  end
end

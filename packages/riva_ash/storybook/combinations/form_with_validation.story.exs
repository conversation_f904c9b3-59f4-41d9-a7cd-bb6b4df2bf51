defmodule RivaAshWeb.Storybook.Combinations.FormWithValidation do
  use PhoenixStorybook.Story, :component
  alias RivaAshWeb.Components.AtomicComponents

  def function, do: &AtomicComponents.molecules/0

  def variations do
    [
      %Variation{
        id: :default,
        attributes: %{
          form: %{
            errors: [],
            values: %{email: "<EMAIL>"},
            validate_on: "blur"
          }
        },
        slots: [
          """
          <AtomicComponents.molecules.card>
            <.live_component module={RivaAshWeb.Core.Forms.RealTimeValidator} />
            <AtomicComponents.molecules.form_field field={:email} label="Email" errors={@form.errors}>
              <AtomicComponents.atoms.input
                type="email"
                value={@form.values[:email]}
                phx-keyup="validate_email"
              />
            </AtomicComponents.molecules.form_field>
            <div class="mt-4">
              <AtomicComponents.atoms.button
                variant="primary"
                phx-disable-with="Validating..."
              >
                Save Changes
              </AtomicComponents.atoms.button>
            </div>
          </AtomicComponents.molecules.card>
          """
        ]
      },
      %Variation{
        id: :with_errors,
        attributes: %{
          form: %{
            errors: [email: {"can't be blank", [validation: :required]}],
            values: %{email: ""}
          }
        },
        slots: [
          """
          <AtomicComponents.molecules.card variant="error">
            <AtomicComponents.molecules.form_field field={:email} label="Email" errors={@form.errors}>
              <AtomicComponents.atoms.input type="email" value={@form.values[:email]} state="error"/>
            </AtomicComponents.molecules.form_field>
            <div class="mt-4">
              <AtomicComponents.atoms.button variant="disabled">Save Changes</AtomicComponents.atoms.button>
            </div>
          </AtomicComponents.molecules.card>
          """
        ]
      }
    ]
  end
end

defmodule RivaAshWeb.Storybook.Combinations.ReservationCard do
  use PhoenixStorybook.Story, :component
  alias RivaAshWeb.Components.AtomicComponents

  def function, do: &AtomicComponents.molecules/0

  def variations do
    [
      %Variation{
        id: :default,
        attributes: %{
          reservation: %{
            id: "RSV-001",
            date: Date.utc_today(),
            status: :confirmed,
            customer: "John Doe",
            items: ["Table #5", "Chair set"],
            availability: %{total: 15, booked: 8}
          }
        },
        slots: [
          """
          <AtomicComponents.molecules.card>
            <div class="flex items-center gap-4 mb-4">
              <AtomicComponents.atoms.badge
                id="availability-badge"
                phx-click="update_calendar"
                color="green"
                label={"#{@reservation.availability.booked}/#{@reservation.availability.total}"
              />
              <AtomicComponents.organisms.calendar_view
                id="reservation-calendar"
                selected_date={@reservation.date}
                phx-update="ignore"
              />
            </div>
          </AtomicComponents.molecules.card>
          """
        ]
      },
      %Variation{
        id: :state_managed,
        slots: [
          """
          <AtomicComponents.molecules.card>
            <div class="flex items-center justify-between mb-4">
              <AtomicComponents.atoms.badge color="green" label={@reservation.status}/>
              <AtomicComponents.atoms.text color="gray-600" content={@reservation.id}/>
            </div>
            <AtomicComponents.organisms.calendar_view selected_date={@reservation.date}/>
            <div class="mt-4 space-y-2">
              <AtomicComponents.atoms.text weight="semibold" content="Customer: #{@reservation.customer}"/>
              <AtomicComponents.atoms.text content="Items: #{Enum.join(@reservation.items, ", ")}"/>
            </div>
          </AtomicComponents.molecules.card>
          """
        ]
      },
      %Variation{
        id: :with_conflict,
        attributes: %{
          reservation: %{
            id: "RSV-002",
            date: Date.utc_today(),
            status: :conflict,
            customer: "Jane Smith",
            items: ["VIP Booth"]
          }
        },
        slots: [
          """
          <AtomicComponents.molecules.card variant="alert">
            <AtomicComponents.atoms.badge color="red" label={@reservation.status}/>
            <AtomicComponents.organisms.calendar_view selected_date={@reservation.date} highlighted_dates={[Date.utc_today()]}/>
            <div class="mt-4">
              <AtomicComponents.atoms.text weight="bold" content="Conflict Detected!"/>
              <AtomicComponents.atoms.text content="Customer: #{@reservation.customer}"/>
            </div>
          </AtomicComponents.molecules.card>
          """
        ]
      }
    ]
  end
end

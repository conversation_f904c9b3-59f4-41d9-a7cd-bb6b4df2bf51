defmodule Storybook.Root do
  # See https://hexdocs.pm/phoenix_storybook/PhoenixStorybook.Index.html for full index
  # documentation.

  use PhoenixStorybook.Index

  def folder_icon, do: {:fa, "book-open", :light, "psb-mr-1"}
  def folder_name, do: "Storybook"

  def entry("welcome") do
    [
      name: "Welcome Page",
      icon: {:fa, "hand-wave", :thin}
    ]
  end

  def entry("design_tokens") do
    [
      name: "Design Tokens",
      icon: {:fa, "palette", :thin}
    ]
  end

  def entry("property_testing") do
    [
      name: "Property-Based Testing",
      icon: {:fa, "flask", :thin}
    ]
  end
end
